
// 这里是原生库的引用
import Foundation
import Foundation
// UTS内置对象的引用
import DCloudUTSFoundation
import UserNotifications
import SwiftUI
import AVFoundation
import Foundation
import CommonCrypto
 import UserNotifications
 import AVFoundation
 
 

 
public class KeepHelper {
	
static var shared=KeepHelper();
var callback: ((_ data:Bool) -> Void)?;

	func setNotListener(_ callback: @escaping (_ data:Bool) -> Void){
		NotificationCenter.default.addObserver(
		    self,
		    selector: #selector(handleAudioInterruption(_:)),
		    name: AVAudioSession.interruptionNotification,
		    object: nil
		)
		self.callback=callback
	}
	
	@objc func handleAudioInterruption(_ notification: Notification) {
	    guard let info = notification.userInfo,
	          let typeValue = info[AVAudioSessionInterruptionTypeKey] as? UInt,
	          let type = AVAudioSession.InterruptionType(rawValue: typeValue) else { return }
	
	    switch type {
	    case .began:
			
	        //print("音频通道被占用（例如来电）‌:ml-citation{ref="1,3" data="citationList"}")
			self.callback?(false);
	    case .ended:
	        print("音频通道已释放")
			self.callback?(true);
	    default: break
	    }
	}
	
	static func pauseAudio(){
		audioPlayer?.pause()
	}
	
   static var startLongTime=Date().timeIntervalSince1970*1000;
	 static  let audioSession = AVAudioSession.sharedInstance()
   static func getListTime()->Double{
       return Date().timeIntervalSince1970*1000-startLongTime;
      
   }
   
   
   static  func getFormatStr(_ h: Int,_ m: Int,_ s: Int)->String{
	   return String(format:"%02d", h)+":"+String(format:"%02d", m)+":"+String(format: "%02d", s)+"";
	   
   }
   
   
	
   static func checkNotifation()->Bool{
         UNUserNotificationCenter.current().getNotificationSettings { settings in
                let isNotificationsEnabled = settings.authorizationStatus == .authorized
                //completion(isNotificationsEnabled)
            }
        return true;
       
    }
    
   static func openNotifationSettings() {
        print("openNotifationSettings enter")
        guard let appSettings = URL(string: UIApplication.openSettingsURLString) else {
            print("openNotifationSettings null")
            return
        }
        
        if UIApplication.shared.canOpenURL(appSettings) {
            UIApplication.shared.open(appSettings)
        }
     //   completionHandler();
    }
   static func sendNotifation(_ title:String,_ msg:String,_ soundEnable:Bool,_ brageEnable:Bool){
         
        // 请求权限
        UNUserNotificationCenter.current().requestAuthorization(options: [.alert, .badge, .sound]) { granted, error in
            guard granted else { return }
            // 创建通知内容
            let content = UNMutableNotificationContent()
            content.title = title
            content.body = msg;
			if(soundEnable){
				content.sound = UNNotificationSound.default
			}else{
				content.sound = nil;
			}
			if(brageEnable){
				content . badge = 1
			}else{
				content . badge = 0;
			}
			
			
			
            
            
            // 创建触发器
            let trigger = UNTimeIntervalNotificationTrigger(timeInterval: 1, repeats: false)
            
            // 创建请求
            let request = UNNotificationRequest(identifier: "Keep", content: content, trigger: trigger)
            
            // 添加请求到通知中心
            UNUserNotificationCenter.current().add(request) { error in
                if let error = error {
                    print("Uh oh! We had an error: \(error)")
                }
            }
        }
        
    }
    
    
  static var  audioPlayer: AVAudioPlayer?
  
   static func stopPlayMusic(){
   	  audioPlayer?.stop();
   }



	// func setAudioNotifation(){
	// 	NotificationCenter.default.addObserver(self, selector: #selector(handleInterruption), 
	// 	                                     name: AVAudioSession.interruptionNotification, object: nil)
		
		
	// }

 //   @objc func handleInterruption(notification: Notification) {
 //       guard let info = notification.userInfo,
 //             let typeValue = info[AVAudioSessionInterruptionTypeKey] as? UInt,
 //             let type = AVAudioSession.InterruptionType(rawValue: typeValue) else { return }
       
 //       if type == .ended {
	// 	  KeepHelper.enalbePlay();
 //          KeepHelper.audioPlayer?.play()
 //       }
 //   }
   
   
  static func playBackgroundMusic(_ filename: String) {
	  // 检测其他应用音频播放
	          if audioSession.isOtherAudioPlaying {
	          //     print("⚠️ 检测到其他音频正在播放‌:ml-citation{ref="1,2" data="citationList"}")
	          
				return;
			  }

		KeepHelper.enalbePlay();
	  
	  
      // 获取音频文件的URL
      if let fileURL = Bundle.main.url(forResource: filename, withExtension: "mp3") {
          do {
              // 初始化音频播放器
              audioPlayer = try AVAudioPlayer(contentsOf: fileURL)
             
			  audioPlayer?.numberOfLoops = -1 // 设置无限循环播放
					// 准备播放
              audioPlayer?.prepareToPlay()
              // 播放音乐
              audioPlayer?.play()
          } catch {
              // 错误处理
              print("Could not load audio file.")
          }
      } else {
          print("Could not find audio file named \(filename).")
      }
  }
  
  
 
  
  
 
   static var backgroundTaskIdentifier: UIBackgroundTaskIdentifier?
   static var backgroundTaskTimer: Timer?;
   
   static var isRunning:Bool=false;

 static func startBackgroundTimer(_ callback: @escaping (_ data:String) -> Void) {
	 KeepHelper.endBackgroundTask();
	 KeepHelper.isRunning=true;
	 backgroundTaskIdentifier = UIApplication.shared.beginBackgroundTask(expirationHandler: {
	            // 如果时间到了，系统会调用这个闭包
	          //  print("Background task expired, ending task.")
	           // KeepHelper.endBackgroundTask()
	            callback("1");
	            // 尝试重新启动后台任务（注意：这并不保证系统会允许）
	            KeepHelper.startBackgroundTimer(callback)
	        })
	       KeepHelper.performBackgroundTask(callback)
	       
}


 static func performBackgroundTask(_ callback: @escaping (_ data:String) -> Void) {
        // 模拟一个需要时间的后台任务
        backgroundTaskTimer = Timer.scheduledTimer(withTimeInterval: 5.0, repeats: true) { [] _ in
          //  self?.performBackgroundTask()
                    //self?.performBackgroundTask()
					//audioPlayer?.play()
				if audioSession.isOtherAudioPlaying {
				//     print("⚠️ 检测到其他音频正在播放‌:ml-citation{ref="1,2" data="citationList"}")
				callback("2");
					return;
				}	else{
					if (audioPlayer?.isPlaying==true) {  // `isPlaying` 为 `true` 表示正在播放‌:ml-citation{ref="2,5" data="citationList"}
					    //print("音频正在播放")
						callback("3");
					} else {
						callback("6");
						KeepHelper.enalbePlay();
					    audioPlayer?.play()
					}
				}
					callback("4");
					// callback();
         }
    }
	
	
	
	
	static func isRun()->Bool{
	     return KeepHelper.isRunning;
	 }
	
static func endBackgroundTask() {
	 KeepHelper.isRunning=false;
    if let identifier = backgroundTaskIdentifier {
               UIApplication.shared.endBackgroundTask(identifier)
               backgroundTaskIdentifier = nil
           }
           
           // 无效化定时器
           backgroundTaskTimer?.invalidate()
           backgroundTaskTimer = nil
}


	static func requestNotificationPermission(_ callback: @escaping (_ data:Int) -> Void) {
		let center = UNUserNotificationCenter.current()
		center.requestAuthorization(options: [.alert, .sound, .badge]) { granted, error in
			if granted {
				print("授权成功")
				callback(0);
			 } else if let error = error {
				print("请求失败：\(error.localizedDescription)")
				callback(1);
			} else {
				callback(2);
				print("用户明确拒绝")
				// 可在此处显示自定义引导弹窗
			}
		}
	}
	
	static func openNotfationSetting(){
		if let url = URL(string: UIApplication.openSettingsURLString) {
		    UIApplication.shared.open(url)
		}
		
	}
	
	
	static func enalbePlay(){
		do {
			
			// try  AVAudioSession.sharedInstance().setCategory(.ambient, mode: .default, options: [.mixWithOthers])
		    try AVAudioSession.sharedInstance().setCategory(.playback, mode: .default, options: [.mixWithOthers])
			try AVAudioSession.sharedInstance().setActive(true)
		} catch {
		    print("音频会话激活失败: \(error)")
		}
	}
	
	static func replayAudio(){
		if audioSession.isOtherAudioPlaying {
		//     print("⚠️ 检测到其他音频正在播放‌:ml-citation{ref="1,2" data="citationList"}")
			return;
		}
		// KeepHelper.enalbePlay();
		  // guard let optionsValue = userInfo[AVAudioSessionInterruptionOptionKey] as? UInt else { return }
		  //       let options = AVAudioSession.InterruptionOptions(rawValue: optionsValue)
		  //       if options.contains(.shouldResume) {
		       
		  //       }
		  KeepHelper.enalbePlay()
		   audioPlayer?.play()
	}
	


}

