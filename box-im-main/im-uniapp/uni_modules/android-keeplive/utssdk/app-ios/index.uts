import { UNUserNotificationCenter } from "UserNotifications";
const map = new Map<number, number>()
var t:Double=0;
var isKeep=false;
 var isRun=true;
export class KeepLive{
			
	private notifationTitle:string="app";
	private notifationMsg:string="app";
	private notifationSoundEnable:boolean=false;	
	private notifationBrageEnable:boolean=false;	
	
	constructor(){
		
		t=KeepHelper.getListTime();
	}
	

	public isHavePermision(pername:string): boolean {
	
		return false;
	 }
	public requestPermison(pername:string, callback: (sth:boolean) => void ) {
			
			 
			 
	}
	
	public requesMoretPermison(pername:string[], callback: (sth:boolean) => void ) {
			
	}

	
	
	public isIgnoringBatteryOptimizations():boolean{
		return true;
	}
	
	
	public requestIgnoreBatteryOptimizations():void {
	}
	
	
	
	
	
	public goKeepLiveSetting():void {
	}
	
	
	public doStartApplicationWithPackageName( packagename:string):void{
	}
	
	
	public restartThisApp(){
	}
	
	public setNotifationAcIntent(){
	}
	
	public setNotifationBrodcastIntent(){
	}

	
	
	public checkAppNotification():boolean{
		console.log(KeepHelper.checkNotifation())
		return true;
	}
	
	public startMicSevice():void{
		
	}
	
	
	
	public goNotificationSetting():void{
		 // 请求用户授权发送通知
		 console.log("goNotificationSetting")
		 console.log(KeepHelper.checkNotifation())
		KeepHelper. openNotfationSetting();
		 
		
	}
	
	public function_test(){
		console.log("function_test")
	}
	
	
		public onOpenNotificationSetting(callbak:(on:boolean)=>void){
			callbak(false);
		}
		
		public setMusicEnabled(en:boolean):void{
			
		}
	
	    public  setTitle( title:string):void{
			this.notifationTitle=title;
	    }
	
	    public  setContent(content:string ):void{
			this.notifationMsg=content;
			
	    }
	
	
	    public  setWorkerEnabled( on:boolean):void{
	    }
		
		
		public  setNotifaicationSoundEnable( on:boolean):void{
			this.notifationSoundEnable=on;
		}
		
		public setAutoStartEnable(en:boolean){
		}
		
		public preCreateNotificationChannel(){
		};
		
	    public  setSmallIcon(icon:string ):void{
	        // UTSiOS.
	    }
	
	
	    public  setLargeIcon( icon:string):void{
			
	        
	    }
		
		public  setMusicId( musicId:string):void{
			
		}
		
		public setDebug(d:boolean):void{
		}
	
	
	    public  setBackgroundMusicEnabled( on:boolean):void{
	    }
	
	
	    public  setMusicInterval(t:number):void{
	    }
	
	    public  setWorkOnMainThread( t:boolean):void{
	    }
	
	
	    public  register():void{
			console.log("register")
			// console.log(p);
			isKeep=true;
			KeepHelper.playBackgroundMusic("main");
			// console.log("play1  ")
			KeepHelper.startBackgroundTimer((data:string) => {
				// console.log("-----------"+data)
			}) 
			KeepHelper.shared.setNotListener(function(res:boolean){
				//console.log("setNotListener ",res);
				if(isKeep&&res){
					//console.log("play")
				//	KeepHelper.playBackgroundMusic("test");
					// AudioPlayerManager.shared.playBackgroundMusic()
				}else{
		
				}
			})
			
					
	    }
		public testdata(){
			
			
		}
	
	    public  unregister():void{
			console.log("unregister")
			KeepHelper.endBackgroundTask();
			KeepHelper.stopPlayMusic();
			isKeep=false;
	    }
		
		public isRunning():boolean{
			return KeepHelper.isRun();
		}
		
		public restart():void{
				this.unregister();
				this.register();
		}
		
		
		public  setChannelName( title:String):void{
	    }

		
		public setChannelId(id:string){
	   }
	
	
	    public  updateNotification():void{
			this.checkAppNotifationPer(function(res:number){
				console.log(res)
				if(res==0){
					KeepHelper.sendNotifation(this.notifationTitle,this.notifationMsg,this.notifationSoundEnable,this.notifationBrageEnable)
				}
			})
		
	    }
	
	    public  hideNotification( hide:boolean):void{
			
	    }
		
		 
		 public onAddNotificationClickListener(callback:() => void):void{
		 }
		
		
		taskId1:number=-1;
		 taskId2:number=-1;
		 taskIsNeedRunning:boolean=false;
		 
		 @UTSJS.keepAlive
		public startAleraTask(startTime:number,intervalTime:number,callback:() => void):void{
			this.cancleAleraTask();
			this.taskIsNeedRunning=true;
			var that=this;
			this.taskId1=setTimeout(function(){
				callback();
				if(that.taskIsNeedRunning){
					that.taskId2=setInterval(function(){
						callback();
					},intervalTime.toInt());
				}
			
			},startTime.toInt());
			
		}
		
		
		public  cancleAleraTask():void{
			if(this.taskId1!=-1){
				clearTimeout(this.taskId1);
			}
			if(this.taskId2!=-1){
				clearInterval(this.taskId2);
			}
			this.	taskIsNeedRunning=false;
			this.taskId1=-1;
			this.taskId2=-1;
		}
		
		public onAddBackgroundCallback(callback:(sth:boolean) => void ):void{
			
		}
		
		public onAddScrrenListenerCallback(callback:(sth:boolean) => void ):void{
		}
		
		
		
		
		
		public setChannelImportance(im:number):void{
		}
		
		
		
		
		
		
		public setWakeLock(state:number,tag:string):void{
		}
		
		public  acquire(time:number):void{
				
		}
		
		
		public releaseAcquire():void{
			
		}
		
		public onExecThreadTask(callback:()=>void):void{
			
		}
		@UTSJS.keepAlive
		public startCSystemTimer(time:number,callback:()=>void){
			this.cancleAleraTask();
			var that=this;
		
			that.taskId2=setInterval(function(){
				callback();
			},(time*1000).toInt());
			map.set(that.taskId2,that.taskId2);
		}
		// public  setNotifaicationSoundEnable( on:boolean):void{
			
		// }
		
		public cancelCSystemTimer(){
			if(this.taskId2!=-1){
				clearInterval(this.taskId2);
			}
			map.delete(this.taskId2);
		}
		
		public clearAllCTimer(){
			 map.forEach(function (value : number, key : number) {
			      clearInterval(key)
			})
		}
		
		
		
		@UTSJS.keepAlive
		public startKeepClcokTimer(time:number,callback:()=>void){
			this.startCSystemTimer(time,callback)
		}
		
		public cancelKeepClcokTimer(){
			this.cancelCSystemTimer();
		}
		public cancelAllKeepClcokTimer(){
			this.clearAllCTimer();
		}
		
		
		 public toBackground(){
		}
		
		
		public getLiveTime():number{
			var b=KeepHelper.getListTime();
			return  Number.from(b);
		}
		
		public getLiveStr():string{
			var totalSeconds=Int(this.getLiveTime()/1000)
			;
			console.log(totalSeconds)
			 let hours = Int(totalSeconds / 3600)
			    let minutes = Int((totalSeconds % 3600) / 60)
			    let seconds = Int(totalSeconds % 60)
			// return hours+":"+minutes+":"+seconds;
			// ;
			return KeepHelper.getFormatStr(hours,minutes,seconds)
		}
		
		
		public  setMusicVol(vol:number):void{
		}
		public   hideRecentTask( hide:boolean){
			
		}
		public closeNotifation(){
			
			
		}
		
		
		public setNotifationId(id:number){
		}
		public getNotifationId():number{
			return 0;
		}
		
		
		public  wifilock():void{
		
		}
		
		public  wifiunlock():void{
		
		}
		
		public requestNotifationPer(callback:(r:boolean)=>void):void{
			
		}
		
		public checkNotificationPer():boolean{
			return true;
		}
		
		
		public goAndroidSystemAppSetting(){
			
			
		}
		public setShowChannelBrage(on:boolean){
			this.notifationBrageEnable=on;
		}
		
		
		public checkAppNotifationPer(callback:(res:number)=>void){
			
			KeepHelper.requestNotificationPermission(function(type:Int){
				callback(Number.from(type));
			})
		}
		public setCrashRestartUIEnabled(enable:boolean){
			
		}
}



export class MyPluginClass implements UTSiOSHookProxy {
	// uts 插件创建时的回调。
	onCreate() {
	}
	// 应用正常启动时 (不包括已在后台转到前台的情况)的回调函数。
	applicationDidFinishLaunchingWithOptions(application: UIApplication | null, launchOptions: Map<UIApplication.LaunchOptionsKey, any> | null = null): boolean {
	    UIApplication.shared.setMinimumBackgroundFetchInterval(30) // 1小时

		console.log("applicationDidFinishLaunchingWithOptions")
	    return false
	}
	// 远程通知注册成功时的回调函数。（打自定义基座时需要勾选 push 模块）
	didRegisterForRemoteNotifications(deviceToken: Data | null) {
		console.log("didRegisterForRemoteNotifications")
		// Push.registerDeviceToken(deviceToken!)
	}
	// 远程通知注册失败时的回调函数。（打自定义基座时需要勾选 push 模块）
	didFailToRegisterForRemoteNotifications(error: NSError | null) {
		console.log("didFailToRegisterForRemoteNotifications")
		
	}
	// 应用收到远程通知时的回调函数。（打自定义基座时需要勾选 push 模块）
	didReceiveRemoteNotification(userInfo: Map<AnyHashable, any> | null) {
		console.log("didReceiveRemoteNotification")
	}
	// 应用收到本地通知时的回调函数。（打自定义基座时需要勾选 push 模块）
	didReceiveLocalNotification(notification: UILocalNotification | null) {
		console.log("didReceiveLocalNotification")

	}
	// 通过 url scheme 方式唤起 app 时的回调函数。(iOS9 之前的系统回调此方法，iOS9 之后的系统请使用 applicationOpenURLOptions)
	applicationHandleOpenURL(application: UIApplication | null, url: URL | null) : boolean {
		console.log("applicationHandleOpenURL")
	    return true
	}
	// 通过 url scheme 方式唤起 app 时的回调函数。
	applicationOpenURLOptions(app: UIApplication | null, url: URL, options: Map<UIApplication.OpenURLOptionsKey, any> | null = null) : boolean {
	  console.log("applicationOpenURLOptions")
		return true
	}
	// 当应用从活动状态主动变为非活动状态的时的回调函数。
	applicationWillResignActive(application: UIApplication | null) {
		console.log("applicationWillResignActive")
	}
	// 应用完全激活时的回调函数。
	applicationDidBecomeActive(application: UIApplication | null) {
		console.log("applicationDidBecomeActive")
	}
	// 应用程序进入后台时的回调函数。
	applicationDidEnterBackground(application: UIApplication | null) {
		console.log("did enter background",isKeep)
		if(isKeep){
			//KeepHelper.playBackgroundMusic("test");
		}
		

	}
	// 当应用在后台状态，将要进入到前台运行时的回调函数。
	applicationWillEnterForeground(application: UIApplication | null) {
	    console.log("applicationWillEnterForeground")
		if(isKeep){
			//KeepHelper.stopPlayMusic();
		}
		
	}
	// 应用程序的 main 函数。
	applicationMain(argc: Int32, argv: UnsafeMutablePointer<UnsafeMutablePointer<CChar> | null>) {
	    console.log("applicationMain")
	}
	// 当应用程序接收到与用户活动相关的数据时调用此方法，例如，当用户使用 Universal Link 唤起应用时。
	applicationContinueUserActivityRestorationHandler(application: UIApplication | null, userActivity: NSUserActivity | null, restorationHandler: ((res: [any] | null) => void) | null = null) : boolean {

	    return true
	}
}
