import CactusHelp from 'com.gyf.xtfcactus.CactusHelp';

import  KeepLiveUtils from 'com.gyf.xtfcactus.KeepLiveUtils'
import Context from "android.content.Context";
import PowerManager from 'android.os.PowerManager';
import Exception from 'java.lang.Exception'
import WifiManager from 'android.net.wifi.WifiManager';
import WifiLock from 'android.net.wifi.WifiManager.WifiLock';
import Build from "android.os.Build";
import Settings from "android.provider.Settings";

import Intent from "android.content.Intent";
import Uri from 'android.net.Uri';
import NotificationManagerCompat from "androidx.core.app.NotificationManagerCompat"


export class KeepLive{
	helper:CactusHelp;
	public isHavePermision(pername:string): boolean {
		return	UTSAndroid.checkSystemPermissionGranted(UTSAndroid.getUniActivity()!, [pername])
	 }
	public requestPermison(pername:string, callback: (sth:boolean) => void ) {
			 if(this.isHavePermision(pername)){
				 callback(true);
				 return
			 }
			  UTSAndroid.requestSystemPermission(UTSAndroid.getUniActivity()!, [pername], (_ : boolean, p : string[]) => {
			          console.log(p)
					  callback(true)
			        }, (_ : boolean, p : string[]) => {
			         callback(false)
			          console.log(p)
			        })
			 
			 
			 
	}
	
	public requesMoretPermison(pername:string[], callback: (sth:boolean) => void ) {
				var have=true;	
				for(var per=0;per<pername.length;per++){
					if(!this.isHavePermision(pername[per])){
						have=false;
					}
				}
				if(have){
					 callback(true)
					 return;
				}
				var len=pername.length;
					
			  UTSAndroid.requestSystemPermission(UTSAndroid.getUniActivity()!, pername, (_ : boolean, p : string[]) => {
			          console.log(p)
						  if(p.length==len){
							  callback(true)
						  }
					  // callback(true)
			        }, (_ : boolean, p : string[]) => {
							console.log(p)
							callback(false)
			          
			        })
			 
			 
			 
	}
	// onBackgroundListener: OnBackgroundListener|null=null;
	// onNotificationClickListener :OnNotificationClickListener|null=null;
	// onScreenListener :OnScreenListener|null=null;
	
	// onAleraListener:OnAleraListener|null=null;
	
	mPowerManger:PowerManager|null=null;
	
	constructor(){
		helper=new CactusHelp(UTSAndroid.getAppContext()!)
		// helper.setOnAppBackgroudCallback(this);
		this.setNotifationAcIntent();
		this.setBackgroundMusicEnabled(true);
		mPowerManger=UTSAndroid.getAppContext()!.getSystemService(Context.POWER_SERVICE) as PowerManager;
	}
	
	public isIgnoringBatteryOptimizations():boolean{
		
		return KeepLiveUtils.isIgnoringBatteryOptimizations(UTSAndroid.getAppContext()!);
	}
	
	
	
	
	
	public requestIgnoreBatteryOptimizations():void {
	   KeepLiveUtils.requestIgnoreBatteryOptimizations(UTSAndroid.getUniActivity()!)
	}
	
	
	
	
	
	public goKeepLiveSetting():void {
		KeepLiveUtils.goKeepLiveSetting(UTSAndroid.getAppContext())
	}
	
	
	public doStartApplicationWithPackageName( packagename:string):void{
		 helper.doStartApplicationWithPackageName(UTSAndroid.getAppContext(),packagename);
	}
	
	
	public restartThisApp(){
		this.doStartApplicationWithPackageName(UTSAndroid.getAppContext()!.getPackageName())
	}
	
	public setNotifationAcIntent(){
		helper.setPendingIntent(UTSAndroid.getUniActivity()!)
	}
	
	public setNotifationBrodcastIntent(){
		helper.setNotificationClick()
	}

	
	
	public checkAppNotification():boolean{
		return helper.checkAppNotification(UTSAndroid.getAppContext()!);
	}
	
	public checkNotificationPer():boolean{
		if(UTSAndroid.getAppContext()!.applicationInfo.targetSdkVersion>=33&&Build.VERSION.SDK_INT>=33){
			return this.isHavePermision("android.permission.POST_NOTIFICATIONS");
		}else{
			return this.checkAppNotification();
		}
		
	}
	
	
	
	public startMicSevice():void{
		//helper.startMicSerice();
	}
	
	
	
	public goNotificationSetting():void{
		return helper.goNotificationSetting(UTSAndroid.getAppContext()!);
	}
	
	public requestNotifationPer(callback:(r:boolean)=>void):void{
		if(UTSAndroid.getAppContext()!.applicationInfo.targetSdkVersion>=33&&Build.VERSION.SDK_INT>=33){
				this.requestPermison("android.permission.POST_NOTIFICATIONS",function(res:boolean){
					callback(res);
				});
			}else{
				callback(false);
			}
		
	}
		public onOpenNotificationSetting(callbak:(on:boolean)=>void){
			var that=this;
			UTSAndroid.onAppActivityResume(function(){
				UTSAndroid.offAppActivityResume();
				callbak(that.checkAppNotification());
			});
			this.goNotificationSetting();
		}
		
		
		
		
		public setMusicEnabled(en:boolean):void{
			helper.setMusicEnabled(en);
		}
	
	    public  setTitle( title:string):void{
	        helper.setTitle(title);
	    }
	
	    public  setContent(content:string ):void{
	        helper.setContent(content);
	    }
	
	
	    public  setWorkerEnabled( on:boolean):void{
	        helper.setWorkerEnabled(on);
	    }
		
		
		public  setNotifaicationSoundEnable( on:boolean):void{
			helper.setSoundEnable(on);
		}
		
		public setAutoStartEnable(en:boolean){
			helper.setAutoStartEnable(en);
		}
		
		public preCreateNotificationChannel(){
			helper.preCreateNotificationChannel();
		};
		
		public crash(){
			class MyRun implements Runnable{
				override run(){
					var s="";
					var d=s.split(",")
					var f=d[10];
				}
				
			}
			new Thread(new MyRun()).start();
		}
		
	    public  setSmallIcon(icon:string ):void{
			
			var id=	UTSAndroid.getAppContext()!.getResources().getIdentifier(icon, "drawable", UTSAndroid.getAppContext()!.getPackageName());
			if(id>0){
				helper.setSmallIcon(id);
			}
	        
	    }
	
	
	    public  setLargeIcon( icon:string):void{
			var id=	UTSAndroid.getAppContext()!.getResources().getIdentifier(icon, "drawable", UTSAndroid.getAppContext()!.getPackageName());
			if(id>0){
				helper.setLargeIcon(id);
			}
	        
	    }
		
		public  setMusicId( musicId:string):void{
			var id=	UTSAndroid.getAppContext()!.getResources().getIdentifier(musicId, "raw", UTSAndroid.getAppContext()!.getPackageName());
			if(id>0){
				helper.setMusicId(id);
			}
		}
		
		public setDebug(d:boolean):void{
			helper.setDebug(d);
		}
	
	
	    public  setBackgroundMusicEnabled( on:boolean):void{
	        helper.setBackgroundMusicEnabled(on);
	    }
	
	
	    public  setMusicInterval(t:number):void{
	        helper.setMusicInterval(t.toInt());
	    }
	
	    public  setWorkOnMainThread( t:boolean):void{
	        helper.setWorkOnMainThread(t);
	    }
	
	
	    public  register():void{
			if(!this.checkAppNotification()){
				this.requestNotifationPer(function(res:boolean){
						helper.register(UTSAndroid.getAppContext()!);
				})
			}else{
				helper.register(UTSAndroid.getAppContext()!);
			}
	    }
	
	    public  unregister():void{
	        helper.unregister(UTSAndroid.getAppContext()!);
	    }
		
		public isRunning():boolean{
			return helper.isRunning(UTSAndroid.getAppContext()!);
			
		}
		
		public restart():void{
			helper.restart(UTSAndroid.getAppContext()!);
			//this.updateNotification();
		}
		
		
		public  setChannelName( title:String):void{
	        helper.setChannelName(title);
	    }

		
		public setChannelId(id:string){
			helper.setChannelId(id);
	   }
	
	
	    public  updateNotification():void{
	        helper.updateNotification(UTSAndroid.getAppContext()!);
	    }
	
	    public  hideNotification( hide:boolean):void{
	        helper.hideNotification(hide);
			
	    }
		
		
	
		
	// 	override onBackground(on1:boolean ):void{
	// 		if(this.onBackgroundListener!=null){
	// 			this.onBackgroundListener!.onBack(on1);
	// 		}
	// 	}
		
		
	
		
		
	// 	override  onNotificationClick():void{
	// 		console.log("onNotificationClick")
	// 		if(this.onNotificationClickListener!=null){
	// 			this.onNotificationClickListener!.onClick();
	// 		}
			
			
			
			
	// 	}
		
		// override	 onScrrenListener(on:boolean){
		// 	 if(this.onScreenListener!=null){
		// 		 this.onScreenListener!.screenState(on);
		// 	 }
		//  }
		 
		//  override onAleraTaskExec(){
		// 	 if(this.onAleraListener!=null){
		// 	 	 this.onAleraListener!.onAlera();
		// 	 }
		//  }
		 
		 
		 public onAddNotificationClickListener(callback:() => void):void{
			 class MyOnNotificationClickListener implements  CactusHelp.OnNotificationClickListener{
					override onNotificationClick():void{
						callback();
					}
			 }
			 helper.setOnNotificationClickListener(new MyOnNotificationClickListener());
		 }
		
		
		@UTSJS.keepAlive
		public startAleraTask(startTime:number,intervalTime:number,callback:() => void):void{
			class MyOnAleraListener implements CactusHelp. OnAleraTaskExecListener{
							override onAleraTaskExec():void{
								callback();
					}
			 }
			 helper.setOnAleraTaskExecListener(new MyOnAleraListener())
			 helper.startExeckTask(startTime.toLong(),intervalTime.toLong());
			 
		}
		
		public onAddBackgroundCallback(callback:(sth:boolean) => void ):void{
			class MyOnBackgroundListener implements  CactusHelp.OnBackgroundListener{
				override onBackground(on1:boolean ):void{
					callback(on1);
				}
			}
			helper.setOnBackgroundListener(new MyOnBackgroundListener());
		}
		
		public onAddScrrenListenerCallback(callback:(sth:boolean) => void ):void{
			class MyOnScreenListener implements CactusHelp.OnScrrenListener{
				override onScrrenListener( on:boolean):void{
					console.log("onScrrenListener",on)
						callback(on)
				}
			}
			console.log("onAddScrrenListenerCallback")
			helper.setOnScrrenListener(new MyOnScreenListener());
		}
		
		
		public  cancleAleraTask():void{
			 helper.cancleTask();
		}
		
		
		public setChannelImportance(im:number):void{
			helper.setChannelImportance(im.toInt());
		}
		
		
		
		
		
		mWake:PowerManager.WakeLock|null=null;
		
		public setWakeLock(state:number,tag:string):void{
			this.mWake=	this.mPowerManger!.newWakeLock(state.toInt(),tag);
			this.mWake!.setReferenceCounted(false);
		}
		
		public  acquire(time:number):void{
				try{
					if(time==-1){
						this.mWake!.acquire();
					}else{
						
						this.mWake!.acquire(time.toLong());
					}
				}catch(e:Exception){
				}
		}
		
		
		public releaseAcquire():void{
			try{
				
				
				if (this.mWake != null && this.mWake!.isHeld()) {
				    this.mWake!.release();
				}
			}catch(e:Exception){
				
			}
		}
		
		public onExecThreadTask(callback:()=>void):void{
			UTSAndroid.getDispatcher("io").async(function(_){
				callback();
			},null)
		}
		public onExecMainTask(callback:()=>void):void{
			UTSAndroid.getDispatcher("main").async(function(_){
				callback();
			},null)
		}
		
		@UTSJS.keepAlive
		public startCSystemTimer(time:number,callback:()=>void){
			class MyOnCTimerListener implements CactusHelp.OnCTimerListener{
				override onCExec():void{
					callback();
				}
			}
			helper.startCSystemTimer(time.toInt(),new MyOnCTimerListener());
		}
		
		
		@UTSJS.keepAlive
		public startKeepClcokTimer(time:number,callback:()=>void){
			class MyOnCTimerListener implements CactusHelp.OnTimerCountListener{
				override onTimerCount():void{
					callback();
				}
			}
			helper.startClcokTimer(time.toInt(),new MyOnCTimerListener());
		}
		
		public cancelKeepClcokTimer(){
			helper.cancelClcokTimer();
		}
		public cancelAllKeepClcokTimer(){
			helper.cancelAllClcokTimer();
		}
		
		public cancelCSystemTimer(){
			helper.cancelSystemTimer();
		}
		
		public clearAllCTimer(){
			helper.clearAllCTimer();
		}
		
		
		 public toBackground(){
			UTSAndroid.getUniActivity()!.moveTaskToBack(true);
		}
		
		
		public getLiveTime():number{
			return Number.from(helper.getLiveTime());
		}
		
		public getLiveStr():string{
			return helper.millisToHMS(helper.getLiveTime());
		}
		
		
		public  setMusicVol(vol:number):void{
			helper.setMuiscVol(vol.toFloat());
		}
		
		
		
		public   hideRecentTask( hide:boolean){
			helper.hideRecentTask(hide);
			
		}
		
		public cancelNotifation( id:number){
			 var manger:NotificationManagerCompat = NotificationManagerCompat.from(UTSAndroid.getAppContext()!);
			 manger.cancel(id.toInt());
		}
		
		
		
		public setCrashRestartUIEnabled(hide:boolean){
			helper.setCrashRestartUIEnabled(hide);
		}
		
		public setNotifationId(id:number){
			console.log("xtf setNotifationId",id)
			helper.setNotifationId(id.toInt());
		}
		
		
		public getNotifationId():number{
			return Number.from(helper.getNotifationId());
		}
		
		public closeNotifation(){
			this.updateNotification();
			helper.cancelNotifation();
			
		}
		 wifiLock:WifiLock|null=null;
		
		public  wifilock():void{
			if(this.wifiLock==null){
				var m=	 UTSAndroid.getAppContext()!. getSystemService(Context.WIFI_SERVICE) as  WifiManager;
				this. wifiLock= m.createWifiLock(WifiManager.WIFI_MODE_FULL, "mylock");
			}
		
				this.wifiLock!.acquire();
		}
		
		public  wifiunlock():void{
			if(this.wifiLock!=null){
				this.wifiLock!.release();
			}
			
		}
		
		
		public goAndroidSystemAppSetting(){
				var intent = new Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
			      intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
				  intent.addCategory(Intent.CATEGORY_DEFAULT);
			var uri = Uri.parse("package:" + UTSAndroid.getAppContext()!.packageName);
				intent.setData(uri)
			     UTSAndroid.getUniActivity()!.startActivity(intent);
			
		}
		
		
		public setShowChannelBrage(on:boolean){
			helper.setShowChannelShowBrage(on);
		}
		
		public getPkgName():string{
			return UTSAndroid.getAppContext()!.getPackageName();
			
		}
		
		
		public onRequestIgnoreBatteryOptimizations(callbak:(on:boolean)=>void):void {
			var that=this;
			UTSAndroid.onAppActivityResult((requestCode: Int, resultCode: Int, data?: Intent) => {
				// UTSAndroid.offAppActivityResult();
				if(requestCode == 100){
					// 我们发起的请求
					let eventName = "onAppActivityResult  -  requestCode:" + requestCode + " -resultCode:"+resultCode + " -data:"+JSON.stringify(data);
			    	console.log(eventName);
				}else{
					// 别的代码发起的请求，不要处理
				}
			
			});
			console.log("onRequestIgnoreBatteryOptimizations")
		    KeepLiveUtils.requestIgnoreBatteryOptimizations(UTSAndroid.getUniActivity()!)
		}
		
		
		
		public checkAppNotifationPer(callback:(res:number)=>void){
			callback(this.checkAppNotification()?0:1)
		}
}



	// interface OnBackgroundListener{
	// 	onBack( on:boolean):void;
	// }



	// interface OnNotificationClickListener{
	// 	onClick();
	// }
	
	// interface OnScreenListener{
	// 	screenState(on:boolean)
		
	// }
	
	
	// interface OnAleraListener{
	// 	onAlera();
		
	// }