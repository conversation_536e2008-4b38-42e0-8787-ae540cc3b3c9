## 1.1.62（2025-07-08）
优化
## 1.1.61（2025-07-08）
优化
## 1.1.60（2025-07-08）
优化保活逻辑，修复已知问题
## 1.1.59（2025-07-08）
优化保活逻辑，修复已知问题
## 1.1.58（2025-06-04）
优化
## 1.1.57（2025-05-20）
优化
## 1.1.56（2025-05-20）
优化Android 
## 1.1.55（2025-05-13）
优化ios
## 1.1.54（2025-05-13）
优化iOS端
## 1.1.53（2025-04-30）
优化通知栏，其它优化
## 1.1.52（2025-04-17）
优化已知问题
## 1.1.51（2025-04-15）
优化
## 1.1.50（2025-04-01）
优化
## 1.1.49（2025-03-14）
优化
## 1.1.48（2025-03-12）
web 和微信小程序改uts 端优化
## 1.1.47（2025-03-12）
web 和微信小程序改uts 为js
## 1.1.46（2025-03-11）
优化
## 1.1.45（2025-03-11）
修复已知问题
## 1.1.44（2025-03-07）
新增通知栏角标设置 默认为关闭
其它优化
## 1.1.43（2025-03-04）
修改插件名称
## 1.1.38（2025-03-03）
优化保活逻辑
## 1.1.36（2025-02-26）
优化iOS 端播放逻辑
## 1.1.35（2025-02-24）
优化
## 1.1.34（2025-02-22）
1.增加部分手机休眠的时候wifi 断开连接，使其保持连接
2.ios端增加info。zip 需要的自行解压使用
3.启动保活增加自动申请通知权限，无需再判断打开设置等
## 1.1.32（2025-02-20）
优化首次启动 部分手机卡顿问题
## 1.1.31（2025-02-17）
优化后台运行逻辑，增加程序后台活性
## 1.1.30（2025-02-12）
优化后台多进程保活
## 1.1.29（2025-02-11）
优化
## 1.1.28（2025-02-11）
优化
## 1.1.27（2025-02-05）
优化
## 1.1.26（2025-02-03）
优化
## 1.1.25（2025-02-03）
新增ios 端  
## 1.1.23（2025-01-07）
修复ios 端打包报错问题
## 1.1.22（2025-01-06）
修改已知问题
## 1.1.21（2025-01-06）
优化
## 1.1.20（2025-01-02）
修复取消全部定时器闪退问题
## 1.1.19（2024-12-28）
优化程序逻辑
## 1.1.18（2024-12-22）
优化
## 1.1.17（2024-12-20）
优化
## 1.1.16（2024-12-19）
去掉自启动权限
## 1.1.15（2024-12-16）
优化
## 1.1.14（2024-12-13）
防止后台录音时被释放
## 1.1.13（2024-12-03）
修复后台音乐bug
## 1.1.12（2024-12-02）
其它修改
## 1.1.11（2024-11-30）
修改出现编译错误问题
## 1.1.10（2024-11-27）
修复闪退
## 1.1.9（2024-11-27）
重复提交
## 1.1.8（2024-11-27）
新增唤醒cpu 功能
## 1.1.7（2024-11-12）
新增静默通知
## 1.1.6（2024-11-04）
增加web
## 1.1.5（2024-11-02）
修复已知问题
## 1.1.4（2024-10-31）
修改定时器小于60s 无法取消问题
## 1.1.3（2024-10-31）
增加全局保活
## 1.1.2（2024-10-31）
修复多个定时器错乱问题
## 1.1.1（2024-10-30）
修复已知问题
## 1.0.6（2024-10-28）
修复已知问题
## 1.0.5（2024-10-28）
修复编译出错问题
## 1.0.4（2024-10-25）
修改 回调函数已释放，不能再次执行问题
## 1.0.3（2024-10-08）
修复已知bug
## 1.0.2（2024-09-26）
修复已知bug
## 1.0.1（2024-09-24）
修复已知bug
## 1.0.0（2024-09-19）
初始化
