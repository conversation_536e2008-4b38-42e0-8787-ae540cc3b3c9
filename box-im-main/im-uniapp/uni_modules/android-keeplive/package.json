{"id": "android-keeplive", "displayName": "安卓保活 ios保活 保应用程序稳定后台运行(支持uniapp，uniappx保活 长期维护）", "version": "1.1.62", "description": "android保活/ios保活 为应用程序提供一个稳定的后台运行环境，为您的应用程序保驾护航，防止程序后台被杀，程序唤醒，息屏唤醒，增加程序活性,优化代码执行,保活稳定", "keywords": ["保活", "安卓保活", "", "ios保活", "Android保活", "", "稳定定时器"], "repository": "", "engines": {"HBuilderX": "^3.99"}, "dcloudext": {"type": "uts", "sale": {"regular": {"price": "19.99"}, "sourcecode": {"price": "666.66"}}, "contact": {"qq": "1530948626"}, "declaration": {"ads": "无", "data": "插件不采集任何数据", "permissions": "  <uses-permission android:name=\"android.permission.START_ACTIVITIES\" />\n    <uses-permission android:name=\"android.permission.FOREGROUND_SERVICE\" />\n    <uses-permission android:name=\"android.permission.RECEIVE_BOOT_COMPLETED\" />\n    <uses-permission android:name=\"android.permission.GET_TASKS\" />\n    <uses-permission android:name=\"android.permission.SCHEDULE_EXACT_ALARM\" />\n    <uses-permission android:name=\"android.permission.SET_ALARM\"/>\n    <uses-permission android:name=\"android.permission.USE_EXACT_ALARM\"/>\n    <uses-permission android:name=\"android.permission.SCHEDULE_EXACT_ALARM\" />\n    <uses-permission android:name=\"android.permission.POST_NOTIFICATIONS\" />\n    <uses-permission android:name=\"android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS\" />\n    <uses-permission android:name=\"android.permission.FOREGROUND_SERVICE_MEDIA_PLAYBACK\"/>"}, "npmurl": ""}, "uni_modules": {"dependencies": [], "encrypt": [], "platforms": {"cloud": {"tcb": "y", "aliyun": "y", "alipay": "y"}, "client": {"Vue": {"vue2": "y", "vue3": "y"}, "App": {"app-android": {"minVersion": "19"}, "app-ios": "y", "app-harmony": "u"}, "H5-mobile": {"Safari": "y", "Android Browser": "y", "微信浏览器(Android)": "y", "QQ浏览器(Android)": "y"}, "H5-pc": {"Chrome": "y", "IE": "y", "Edge": "y", "Firefox": "y", "Safari": "y"}, "小程序": {"微信": "y", "阿里": "n", "百度": "n", "字节跳动": "n", "QQ": "n", "钉钉": "n", "快手": "n", "飞书": "n", "京东": "n"}, "快应用": {"华为": "n", "联盟": "n"}}}}}