package com.bx.implatform.api.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bx.imcommon.util.JwtUtil;
import com.bx.implatform.api.dto.LoginTokenDTO;
import com.bx.implatform.api.service.UserApiService;
import com.bx.implatform.api.vo.LoginTokenVO;
import com.bx.implatform.config.props.JwtProperties;
import com.bx.implatform.entity.User;
import com.bx.implatform.exception.GlobalException;
import com.bx.implatform.mapper.UserMapper;
import com.bx.implatform.service.UserService;
import com.bx.implatform.session.APISession;
import com.bx.implatform.session.APISessionContext;
import com.bx.implatform.session.UserSession;
import com.bx.implatform.vo.UserImportVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class UserApiServiceImpl extends ServiceImpl<UserMapper, User> implements UserApiService {

    private final JwtProperties jwtProps;

    private final UserService userService;


    @Override
    public LoginTokenVO token(LoginTokenDTO tokenDTO) {
        String userId = tokenDTO.getUserId();
        APISession apiSession = APISessionContext.getSession();
        String appId = apiSession.getAppId();
        User user = userService.findUserByUserNameAndAppId(userId, appId);
        if(user == null){
            throw new GlobalException("用户不存在");
        }

        // 生成token
        UserSession session = new UserSession();
        session.setAppId(appId);
        session.setUserName(userId);
        session.setUserId(user.getId());
        String strJson = JSON.toJSONString(session);

        String accessToken =
                JwtUtil.sign(user.getId(), strJson, jwtProps.getAccessTokenExpireIn(), jwtProps.getAccessTokenSecret());
        String refreshToken =
                JwtUtil.sign(user.getId(), strJson, jwtProps.getRefreshTokenExpireIn(), jwtProps.getRefreshTokenSecret());
        LoginTokenVO vo = new LoginTokenVO();
        vo.setAccessToken(accessToken);
        vo.setAccessTokenExpiresIn(jwtProps.getAccessTokenExpireIn());
        vo.setRefreshToken(refreshToken);
        vo.setRefreshTokenExpiresIn(jwtProps.getRefreshTokenExpireIn());

        return vo;
    }

    @Override
    public List<String> importUser(List<UserImportVO> users) {
        APISession session = APISessionContext.getSession();
        List<String> ids = new ArrayList<>();
        for(UserImportVO vo : users){
            if(importUser(session.getAppId(), vo)){
                ids.add(vo.getUserId());
            }
        }
        return ids;
    }

    public boolean importUser(String appId, UserImportVO vo) {
        try {
            User user = userService.findUserByUserName(vo.getUserId());
            if(user == null){
                user = new User();
                user.setUserName(vo.getUserId());
                user.setNickName(vo.getNickName());
                user.setPassword("password");
                user.setHeadImage(vo.getHeadImage());
                user.setSource(vo.getSource());
                user.setVip(vo.getVip());
                user.setSuperiorId(vo.getSuperiorId());
                user.setAppId(appId);
                userService.save(user);
            }else{
                user.setNickName(vo.getNickName());
                if(vo.getHeadImage() != null){
                    user.setHeadImage(vo.getHeadImage());
                }
                if(vo.getVip() != null){
                    user.setVip(vo.getVip());
                }
                if(vo.getSource() != null){
                    user.setSource(vo.getSource());
                }
                if(vo.getSuperiorId() != null){
                    user.setSuperiorId(vo.getSuperiorId());
                }
                userService.updateById(user);
            }
            return true;
        }catch (Exception e){
            log.error("import", e);
        }
        return false;
    }
}
