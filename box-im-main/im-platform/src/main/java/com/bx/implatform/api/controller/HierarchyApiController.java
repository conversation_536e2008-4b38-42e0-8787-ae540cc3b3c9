package com.bx.implatform.api.controller;

import com.bx.implatform.api.service.HierarchyApiService;
import com.bx.implatform.api.vo.HierarchyBindVO;
import com.bx.implatform.result.Result;
import com.bx.implatform.result.ResultUtils;
import com.bx.implatform.vo.FriendVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "服务器api-关系层级API")
@RestController
@RequestMapping("/api/hierarchy")
@RequiredArgsConstructor
public class HierarchyApiController {

    private final HierarchyApiService hierarchyApiService;

    @GetMapping("/list")
    @Operation(summary = "下级列表", description = "获取下级列表")
    public Result<List<FriendVO>> findFriends() {
        return ResultUtils.success(hierarchyApiService.subordinates());
    }

    @PostMapping("/bind")
    @Operation(summary = "绑定上下级关系", description = "双方建立上下级关系")
    public Result bind(HierarchyBindVO vo) {
        hierarchyApiService.addHierarchy(vo);
        return ResultUtils.success();
    }

}

