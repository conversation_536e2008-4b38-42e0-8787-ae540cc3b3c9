package com.bx.implatform.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName("im_follow")
public class Follow {
    /**
     * id
     */
    @TableId
    private Long id;

    /**
     * 用户id
     */
    private String userName;

    /**
     * 关注id
     */
    private String followUserName;

    /**
     * 关注用户昵称
     */
    private String followNickName;

    /**
     * 关注用户头像
     */
    private String followHeadImage;

    /**
     * 创建时间
     */
    private Date createdTime;
}
