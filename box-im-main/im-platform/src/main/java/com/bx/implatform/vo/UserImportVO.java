package com.bx.implatform.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

@Data
@Schema(description = "用户导入信息VO")
public class UserImportVO {

    @NotNull(message = "用户id不能为空")
    @Schema(description = "用户id")
    private String userId;

    @NotEmpty(message = "用户昵称不能为空")
    @Length(max = 20, message = "昵称不能大于20字符")
    @Schema(description = "用户昵称不能大于20字符")
    private String nickName;

    @Schema(description = "头像")
    private String headImage;

    @Schema(description = "用户来源")
    private String source;

    @Schema(description = "Vip等级")
    private Integer vip;

    @Schema(description = "上级id")
    private String superiorId;
}
