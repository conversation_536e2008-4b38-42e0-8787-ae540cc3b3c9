package com.bx.implatform.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName("im_hierarchy")
public class Hierarchy {

    /**
     * id
     */
    @TableId
    private Long id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 上级id
     */
    private Long superiorId;

    /**
     * 编号
     */
    private Integer no;

    /**
     * 创建时间
     */
    private Date createdTime;
}
