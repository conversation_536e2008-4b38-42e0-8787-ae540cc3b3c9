package com.bx.implatform.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bx.implatform.entity.Hierarchy;
import com.bx.implatform.mapper.HierarchyMapper;
import com.bx.implatform.service.HierarchyService;
import com.bx.implatform.vo.FriendVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class HierarchyServiceImpl extends ServiceImpl<HierarchyMapper, Hierarchy> implements HierarchyService {
    @Override
    public List<FriendVO> subordinates() {
        return List.of();
    }

    @Override
    public void addHierarchy(Long userId, Long superiorId) {
        // 检查userId是否已经有上级
        Long count = lambdaQuery()
                .eq(Hierarchy::getUserId, userId)
                .count();
        if (count != null && count > 0) {
            throw new IllegalStateException("该用户已存在上级，不能重复添加");
        }
        // 查询该上级已有的下级数量，确定新的no
        Integer maxNo = lambdaQuery()
                .eq(Hierarchy::getSuperiorId, superiorId)
                .select(Hierarchy::getNo)
                .list()
                .stream()
                .map(Hierarchy::getNo)
                .filter(n -> n != null)
                .max(Integer::compareTo)
                .orElse(0);

        int newNo = maxNo + 1;

        // 新建层级关系
        Hierarchy hierarchy = new Hierarchy();
        hierarchy.setUserId(userId);
        hierarchy.setSuperiorId(superiorId);
        hierarchy.setCreatedTime(new Date());
        hierarchy.setNo(newNo);
        save(hierarchy);
    }

    @Override
    public boolean isHierarchy(Long userId, Long friendId) {
        lambdaQuery().eq(Hierarchy::getUserId, userId).eq(Hierarchy::getSuperiorId, friendId).exists();
        return false;
    }
}
