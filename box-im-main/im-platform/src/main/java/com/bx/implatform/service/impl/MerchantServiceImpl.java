package com.bx.implatform.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bx.implatform.entity.Merchant;
import com.bx.implatform.mapper.MerchantMapper;
import com.bx.implatform.service.MerchantService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class MerchantServiceImpl extends ServiceImpl<MerchantMapper, Merchant> implements MerchantService {

    @Override
    public Merchant findByAppId(String appId) {
        LambdaQueryWrapper<Merchant> query = Wrappers.lambdaQuery();
        query.eq(Merchant::getAppId, appId);
        return this.getOne(query);
    }
}
