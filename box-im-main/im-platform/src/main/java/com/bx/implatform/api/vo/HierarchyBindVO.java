package com.bx.implatform.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
@Schema(description = "上级绑定VO")
public class HierarchyBindVO {

    @NotNull(message = "用户id不可为空")
    @Schema(description = "用户id")
    private Long userId;

    @NotNull(message = "上级id不可为空")
    @Schema(description = "上级id")
    private Long superiorId;
}
