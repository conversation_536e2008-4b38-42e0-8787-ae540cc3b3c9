package com.bx.implatform.api.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bx.implatform.api.dto.LoginTokenDTO;
import com.bx.implatform.api.vo.LoginTokenVO;
import com.bx.implatform.entity.User;
import com.bx.implatform.vo.UserImportVO;

import java.util.List;

public interface UserApiService extends IService<User> {

    /**
     * 获取token
     * @param tokenDTO
     * @return
     */
    LoginTokenVO token(LoginTokenDTO tokenDTO);

    /**
     * 导入用户数据
     * @param users
     */
    List<String> importUser(List<UserImportVO> users);

}
