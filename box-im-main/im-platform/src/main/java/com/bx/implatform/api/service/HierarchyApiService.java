package com.bx.implatform.api.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bx.implatform.api.vo.HierarchyBindVO;
import com.bx.implatform.entity.Hierarchy;
import com.bx.implatform.vo.FriendVO;

import java.util.List;

public interface HierarchyApiService extends IService<Hierarchy> {

    List<FriendVO> subordinates();

    void addHierarchy(HierarchyBindVO vo);

    boolean isHierarchy(Long userId, Long friendId);
}
