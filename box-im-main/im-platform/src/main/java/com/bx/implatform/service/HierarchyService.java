package com.bx.implatform.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bx.implatform.entity.Hierarchy;
import com.bx.implatform.vo.FriendVO;

import java.util.List;

public interface HierarchyService  extends IService<Hierarchy> {

    List<FriendVO> subordinates();

    void addHierarchy(Long userId, Long superiorId);

    boolean isHierarchy(Long userId, Long friendId);
}
