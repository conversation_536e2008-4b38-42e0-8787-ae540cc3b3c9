package com.bx.implatform.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName("im_merchant")
public class Merchant {

    @TableId
    private Long id;

    @TableField("appId")
    private String appId;

    @TableField("merchantName")
    private String merchantName;

    @TableField("secretKey")
    private String secretKey;

    private int status;
}
