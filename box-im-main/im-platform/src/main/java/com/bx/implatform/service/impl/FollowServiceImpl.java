package com.bx.implatform.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bx.imclient.IMClient;
import com.bx.imcommon.enums.IMTerminalType;
import com.bx.implatform.api.dto.ApiFollowDTO;
import com.bx.implatform.entity.Follow;
import com.bx.implatform.entity.User;
import com.bx.implatform.mapper.FollowMapper;
import com.bx.implatform.mapper.UserMapper;
import com.bx.implatform.service.FollowService;
import com.bx.implatform.service.UserService;
import com.bx.implatform.vo.FriendVO;
import com.bx.implatform.vo.RelationVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class FollowServiceImpl extends ServiceImpl<FollowMapper, Follow> implements FollowService {

    private final UserMapper userMapper;

    private final UserService userService;

    private final IMClient imClient;

    @Override
    public void follow(String userId, String targetUserId) {
        if (userId.equals(targetUserId)) {
            return;
        }
        LambdaQueryWrapper<Follow> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(Follow::getUserName, userId).eq(Follow::getFollowUserName, targetUserId);
        Follow exist = this.getOne(wrapper);
        if (exist != null) {
            return;
        }
        User target = userMapper.selectById(targetUserId);
        Follow f = new Follow();
        f.setUserName(userId);
        f.setFollowUserName(targetUserId);
        if (target != null) {
            f.setFollowNickName(target.getNickName());
            f.setFollowHeadImage(target.getHeadImage());
        }
        f.setCreatedTime(new Date());
        this.save(f);
    }

    @Override
    public void unfollow(String userId, String targetUserId) {
        LambdaQueryWrapper<Follow> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(Follow::getUserName, userId).eq(Follow::getFollowUserName, targetUserId);
        this.remove(wrapper);
    }

    @Override
    public List<RelationVO> followings(String userName) {
        User user = userService.findUserByUserName(userName);
        List<Follow> follows = this.findByUserId(user.getUserName());
        List<String> followIds = new ArrayList<>();
        for(Follow follow : follows){
            followIds.add(follow.getFollowUserName());
        }
        if(followIds.isEmpty()){
            return Collections.emptyList();
        }
        List<User> fellowUsers = userService.findUserByUserNames(followIds);
        List<Long> ids = fellowUsers.stream().map(User::getId).toList();
        Map<Long, List<IMTerminalType>> terminals = imClient.getOnlineTerminal(ids);
        return fellowUsers.stream().map(u->new RelationVO(u, terminals.get(u.getId()))).toList();
    }

    private List<Follow> findByUserId(String userId) {
        LambdaQueryWrapper<Follow> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(Follow::getUserName, userId);
        return this.list(wrapper);
    }

    @Override
    public List<ApiFollowDTO> follows(List<ApiFollowDTO> follows) {
        List<ApiFollowDTO> fs = new ArrayList<>();
        for(ApiFollowDTO follow : follows){
            try{
                follow(follow.getUserId(), follow.getFollowId());
                fs.add(follow);
            }catch (Exception e){
                log.error("follow error", e);
            }
        }
        return fs;
    }

    @Override
    public List<ApiFollowDTO> unfollows(List<ApiFollowDTO> follows) {
        List<ApiFollowDTO> fs = new ArrayList<>();
        for(ApiFollowDTO follow : follows){
            try{
                unfollow(follow.getUserId(), follow.getFollowId());
                fs.add(follow);
            }catch (Exception e){
                log.error("follow error", e);
            }
        }
        return fs;
    }
}