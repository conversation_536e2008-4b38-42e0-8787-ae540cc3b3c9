package com.bx.implatform.api;

import com.alibaba.fastjson.JSONObject;
import com.bx.implatform.api.dto.LoginTokenDTO;
import com.bx.implatform.api.vo.LoginTokenVO;
import com.bx.implatform.vo.UserImportVO;

import java.util.Collections;

public class UserApiTest extends BaseApiTest {

    public static void main(String[] args) {
//        importUser("1", "user1", "head112", "测试2", 1, null);
//        importUser("100", "user100", "head100", null, 2, null);
        token("1");
    }

    public static LoginTokenVO token(String userId){
        LoginTokenDTO dto = new LoginTokenDTO();
        dto.setUserId(userId);
        String res = post("api/user/token", dto);
        JSONObject obj = JSONObject.parseObject(res);
        LoginTokenVO vo = JSONObject.parseObject(obj.getString("data"), LoginTokenVO.class);
        return vo;
    }

    public static void importUser(String userId, String nick, String head, String source, int vip, String superiorId){
        UserImportVO vo = new UserImportVO();
        vo.setUserId(userId);
        vo.setNickName(nick);
        vo.setHeadImage(head);
        vo.setSource(source);
        vo.setVip(vip);
        vo.setSuperiorId(superiorId);
        post("api/user/import", Collections.singletonList(vo));
    }
}
