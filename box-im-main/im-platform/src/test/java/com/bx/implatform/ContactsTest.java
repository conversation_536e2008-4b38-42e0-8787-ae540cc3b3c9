package com.bx.implatform;

import com.bx.implatform.api.UserApiTest;
import org.springframework.http.ResponseEntity;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

public class ContactsTest extends BaseTest{

    public static void main(String[] args) {
        String token = UserApiTest.token("1").getAccessToken();
        login(token);
        superior();
        subordinateList();
        friendList();
        followList();
    }

    public static void superior(){
        post("/user/superior");
    }

    public static void subordinateList(){
        post("/user/subordinate/list");
    }

    public static void friendList(){
        post("/friend/list");
    }

    public static void followList(){
        post("/follow/list");
    }
}
