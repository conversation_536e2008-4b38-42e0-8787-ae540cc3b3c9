package com.bx.implatform.api;

import com.bx.implatform.api.dto.ApiFollowDTO;
import com.bx.implatform.api.dto.LoginTokenDTO;
import com.bx.implatform.vo.UserImportVO;

import java.util.Collections;

public class FollowApiTest extends BaseApiTest {

    public static void main(String[] args) {
        remove("1", "100");
    }

    public static void add(String userId, String followId){
        ApiFollowDTO dto = new ApiFollowDTO();
        dto.setUserId(userId);
        dto.setFollowId(followId);
        post("api/follow/add", Collections.singletonList(dto));
    }

    public static void remove(String userId, String followId){
        ApiFollowDTO dto = new ApiFollowDTO();
        dto.setUserId(userId);
        dto.setFollowId(followId);
        post("api/follow/remove", Collections.singletonList(dto));
    }
}
