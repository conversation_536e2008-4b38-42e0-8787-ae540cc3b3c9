package com.bx.implatform.api;

import com.alibaba.fastjson.JSONObject;
import com.bx.implatform.BaseTest;
import com.bx.implatform.util.SignUtil;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;

import java.util.HashMap;

public class BaseApiTest extends BaseTest {

    private static String appId = "1001";

    private static String secretKey = "DIASDUAQAS";

    public static String post(String path, Object obj){
        HttpHeaders headers = new HttpHeaders();
        headers.set("appId", appId);
        String time = Long.toString(System.currentTimeMillis());
        headers.set("time", time);
        headers.set("sign", SignUtil.sign(appId + time, secretKey));
        headers.set("content-type", "application/json");
        HttpEntity<String> entity = new HttpEntity<>(JSONObject.toJSONString(obj), headers);
        ResponseEntity<String> res = restTemplate.exchange(getUrl(path), HttpMethod.POST, entity, String.class);
        String body = res.getBody();
        System.out.println(body);
        return body;
    }
}
