package com.bx.implatform.api;

import com.bx.implatform.BaseTest;
import com.bx.implatform.api.vo.HierarchyBindVO;
import org.springframework.http.ResponseEntity;

import java.util.HashMap;
import java.util.Map;

public class HierarchyApiTest extends BaseTest {

    public static void main(String[] args) {

        bind(1, 2);
    }

    public static void bind(long userId, long superiorId){
        HierarchyBindVO vo = new HierarchyBindVO();
        vo.setUserId(userId);
        vo.setSuperiorId(superiorId);
        post("api/hierarchy/bind", vo);
    }
}
