package com.bx.implatform;

import com.alibaba.fastjson.JSONObject;
import com.bx.implatform.util.SignUtil;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

public class BaseTest {

    public static TestRestTemplate restTemplate = new TestRestTemplate();

    public static String host = "http://127.0.0.1:8880/";

    public static String getUrl(String url){
        return host + url;
    }

    public static String accessToken;

    public static String post(String path, Object obj){
        HttpHeaders headers = new HttpHeaders();
        headers.set("accessToken", accessToken);
        headers.set("content-type", "application/json");
        HttpEntity<String> entity = null;
        if(obj != null){
            entity = new HttpEntity<>(JSONObject.toJSONString(obj), headers);
        }else{
            entity = new HttpEntity<>(headers);
        }
        ResponseEntity<String> res = restTemplate.exchange(getUrl(path), HttpMethod.POST, entity, String.class);
        String body = res.getBody();
        System.out.println(body);
        return body;
    }

    public static String post(String path){
        return post(path, null);
    }

    public static void login(String token){
        accessToken = token;
        post("/login", null);
    }

}
